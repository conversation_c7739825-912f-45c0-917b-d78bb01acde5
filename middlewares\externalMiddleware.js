// File: processor/middlewares/externalMiddleware.js
const logger = require("../config/logger");
const { v4: uuidv4 } = require("uuid");

/**
 * Middleware for external events (no internal try/catch so errors bubble to Middy).
 * @param {Object} options
 * @param {Object} options.application       - The application type DB instance creating the trace.
 * @param {String} options.application.display_name       - The application type name creating the trace.
 * @param {Object} options.function     - Function ID associated with this trace.
 * @param {Object} options.models
 * @param {Object} options.models.EventTrace - Sequelize model for event_traces.
 * @param {Object} options.models.TraceAction - Sequelize model for trace_actions.
 */
module.exports = function eventTraceMiddleware(options) {
  if (
    !options ||
    !options.function ||
    !options.application ||
    !options.models ||
    !options.models.EventTrace ||
    !options.models.TraceAction
  ) {
    throw new Error(
      "eventTraceMiddleware requires options with properties: application, models.EventTrace, models.TraceAction"
    );
  }

  const { EventTrace, TraceAction } = options.models;
  return {
    // Called before the handler runs: create a trace and initial action.
    before: async (handler) => {
      const traceId = uuidv4();

      // Create the EventTrace row
      const trace = await EventTrace.create({
        trace_id: traceId,
        function_id: options.function.function_id || null,
      });

      // Store traceId on the Middy handler
      handler.context = handler.context || {};
      handler.context.trace_id = traceId;
      handler.context.function = options.function;

      logger.info(
        `[BEFORE] Created trace application: ${trace.trace_id} for endpoint ${options.endpoint}`
      );

      // Return the Promise so Middy waits for this write
      await TraceAction.create({
        trace_action_id: uuidv4(),
        trace_id: traceId,
        status: 0, // pending
        message: `Trace started from application: ${options.application.display_name} on function: ${options.endpoint}`,
      });
    },

    // Called after the handler succeeds: record completion
    after: async (handler) => {
      const traceId = handler.context.trace_id;
      logger.info(
        `[AFTER] Trace ${traceId}. Successfully processed external event in function`
      );
      await TraceAction.create({
        trace_action_id: uuidv4(),
        trace_id: traceId,
        status: 1, // completed
        message: "Trace completed successfully",
      });
    },

    onError: async (handler) => {
      const traceId = handler.context?.trace_id || "unknown-trace";
      const errorMessage = handler.error?.message || "Unknown error";

      try {
        logger.error(`[ERROR] Trace ${traceId} failed: ${errorMessage}`);

        // Only record if trace was initialized
        if (traceId !== "unknown-trace") {
          await TraceAction.create({
            trace_action_id: uuidv4(),
            trace_id: traceId,
            status: 2,
            message: errorMessage
          });
        }
      } catch (dbError) {
        logger.error(
          `[ERROR] Failed to log error for trace ${traceId}`,
          dbError
        );
      }
    }
  };
};
