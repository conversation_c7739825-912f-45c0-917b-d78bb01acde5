{"mappingType": "columnName", "mappings": {"email": "Identity.email", "firstName": "Identity.first_name", "lastName": "Identity.last_name", "middleName": "Identity.middle_name", "employeeId": "Identity.eid", "identityType": "Identity.identity_type", "nationalId": "Identity.national_id", "suffix": "Identity.suffix", "mobile": "Identity.mobile", "startDate": "Identity.start_date", "endDate": "Identity.end_date", "status": "Identity.status", "company": "Identity.company", "organization": "Identity.organization", "companyCode": "Identity.company_code", "jobTitle": "Identity.job_title", "jobCode": "Identity.job_code"}, "columnIndex": {"0": "Identity.email", "1": "Identity.first_name", "2": "Identity.last_name", "3": "Identity.middle_name", "4": "Identity.eid", "5": "Identity.identity_type", "6": "Identity.national_id", "7": "Identity.suffix", "8": "Identity.mobile", "9": "Identity.start_date", "10": "Identity.end_date", "11": "Identity.status", "12": "Identity.company", "13": "Identity.organization", "14": "Identity.company_code", "15": "Identity.job_title", "16": "Identity.job_code"}, "required": ["email", "firstName", "lastName"], "validation": {"email": {"type": "email", "required": true}, "firstName": {"type": "string", "required": true}, "lastName": {"type": "string", "required": true}, "identityType": {"type": "number"}, "status": {"type": "number"}}}