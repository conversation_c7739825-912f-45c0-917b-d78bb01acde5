{"name": "event_actions", "version": "1.0.0", "description": "Event actions middleware or processor", "repository": {"type": "git", "url": "https://git.onetalkhub.com/care/care-dataflow-processors"}, "license": "ISC", "author": "<PERSON><PERSON><PERSON>", "main": "index.js", "scripts": {"hl7": "nodemon index.js --queue=hl7_queue", "hl7store": "nodemon index.js --queue=hl7_store", "patad": "nodemon index.js --queue=patient_admission_procesor", "notification": "nodemon index.js --queue=notification_queue", "email": "nodemon index.js --queue=email_queue", "text": "nodemon index.js --queue=text_queue", "hrdata": "nodemon index.js --queue=hr_csv_data", "dev": "nodemon index.js", "db": "node scripts/dbSync.js", "db:refresh": "node scripts/dbSync.js refresh"}, "dependencies": {"@middy/core": "^6.1.6", "amqplib": "^0.10.7", "bcrypt": "^5.1.1", "config": "^3.3.12", "dotenv": "^16.5.0", "ejs": "^3.1.10", "joi": "^17.13.3", "json-rules-engine": "^7.3.1", "keyv": "^5.3.3", "middy": "^0.36.0", "nodemailer": "^6.10.1", "p-limit": "^6.2.0", "pg": "^8.14.1", "sequelize": "^6.37.7", "sharp": "^0.34.1", "telnyx": "^2.0.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}}